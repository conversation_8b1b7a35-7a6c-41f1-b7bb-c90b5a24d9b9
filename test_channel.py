#!/usr/bin/env python3
"""
Quick test script to verify the bot can send messages to the channel
"""

import asyncio
import os
from telegram import Bot

async def test_channel_access():
    """Test if the bot can send messages to the channel"""
    
    bot_token = os.getenv('TELEGRAM_BOT_TOKEN', '**********************************************')
    channel_id = "-4897398016"
    
    bot = Bot(token=bot_token)
    
    test_message = """
🧪 **CHANNEL TEST MESSAGE**

Testing bot access to the channel.

✅ If you can see this message, the bot is properly configured!
🤖 Bot token: Working
📢 Channel access: Working
🔔 Ready to send Singapore concert notifications

*This is a test message - you can ignore it*
    """
    
    try:
        print(f"🤖 Testing bot access to channel {channel_id}...")
        
        # Try to send a message
        message = await bot.send_message(
            chat_id=channel_id,
            text=test_message,
            parse_mode='Markdown'
        )
        
        print("✅ SUCCESS! Test message sent to channel")
        print(f"📱 Message ID: {message.message_id}")
        print(f"📢 Channel: {channel_id}")
        print()
        print("🎉 The bot is ready to send Singapore concert notifications!")
        
    except Exception as e:
        print("❌ ERROR: Could not send message to channel")
        print(f"Error details: {e}")
        print()
        print("🔧 Possible solutions:")
        print("1. Make sure the bot is added to the channel")
        print("2. Give the bot permission to send messages")
        print("3. Check if the channel ID is correct")
        print("4. Verify the bot token is valid")

def main():
    """Main function"""
    print("🧪 YUURI Bot Channel Test")
    print("=" * 30)
    print()
    
    # Check if bot token is available
    bot_token = os.getenv('TELEGRAM_BOT_TOKEN')
    if not bot_token:
        print("⚠️  TELEGRAM_BOT_TOKEN not set, using default from script")
    else:
        print("✅ TELEGRAM_BOT_TOKEN found")
    
    print(f"📢 Testing channel: -4897398016")
    print()
    
    # Run the test
    asyncio.run(test_channel_access())

if __name__ == "__main__":
    main()
