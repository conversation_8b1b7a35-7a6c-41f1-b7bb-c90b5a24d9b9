# YUURI Asia Tour 2025 Monitor

This script monitors the YUURI Asia Tour 2025 website for updates and compares concert information between Singapore and Hong Kong.

## Features

- **Intelligent Monitoring**: Checks the website at random intervals (5-15 minutes)
- **Dual Parsing**: Uses normal HTML parsing with OpenAI fallback when content is unclear
- **Change Detection**: Only alerts when significant changes are detected
- **Data Persistence**: Saves data to JSON files for comparison
- **Robust Error Handling**: Continues monitoring even if individual checks fail

## Usage

### One-time Data Fetch
```bash
python yuuri.py
```

### Continuous Monitoring
```bash
python yuuri.py monitor
```

## Setup

1. **Install Dependencies**:
   ```bash
   pip install requests beautifulsoup4 openai
   ```

2. **Set OpenAI API Key** (optional, for intelligent fallback):
   ```bash
   export OPENAI_API_KEY='your-api-key-here'
   ```

## How It Works

### Normal Operation
1. Fetches website content using requests
2. Parses HTML with BeautifulSoup
3. Extracts concert information using predefined patterns
4. Compares with previous data to detect changes

### Intelligent Fallback
When normal parsing fails or returns incomplete data:
1. Sends HTML content to OpenAI GPT-4o-mini
2. Uses AI to understand and extract concert information
3. Returns structured data in the same format

### Monitoring Loop
- Runs continuously with random intervals (5-15 minutes)
- Detects significant changes in:
  - Concert dates
  - Venue information
  - Ticket prices and availability
  - Show times
  - General status updates

## Output Files

- `yuuri_concert_data.json`: Latest concert data
- `last_change.txt`: Timestamp of last detected change

## Example Output

```
🎵 Starting YUURI Asia Tour 2025 Monitor
==================================================

🔍 Check #1 at 2025-07-25 14:30:15
✅ Successfully parsed with normal method
🚨 CHANGES DETECTED!

📊 COMPARISON:
============================================================
YUURI ASIA TOUR 2025 - SINGAPORE vs HONG KONG COMPARISON
============================================================

📍 VENUE COMPARISON:
Singapore: The Star Theatre
Hong Kong: AXA WONDERLAND, West K, Hong Kong

💰 TICKET PRICES:
Singapore: No pricing information available yet
Hong Kong:
  • Zone A Early Access: HKD $1,980 (SOLD OUT)
  • Zone A: HKD $1,480 (SELLING FAST)
  • Zone B: HKD $1,280 (SELLING FAST)
  • Zone C: HKD $880 (SOLD OUT)

😴 Sleeping for 8 minutes 23 seconds...
```

## Error Handling

- Network errors: Retries after 5 minutes
- Parsing errors: Falls back to OpenAI analysis
- OpenAI errors: Continues with normal parsing only
- Keyboard interrupt: Graceful shutdown

## Cost Considerations

- OpenAI API calls are only made when normal parsing fails
- Uses cost-effective GPT-4o-mini model
- Limits content sent to API to avoid excessive token usage
