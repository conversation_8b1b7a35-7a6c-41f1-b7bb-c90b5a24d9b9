# YUURI Asia Tour 2025 Singapore Monitor

This script monitors the YUURI Asia Tour 2025 website specifically for **Singapore concert updates**. It uses Hong Kong data as a reference template for what complete Singapore data should look like.

## Features

- **🇸🇬 Singapore-Focused**: Monitors only Singapore concert updates
- **🤖 Intelligent Parsing**: Uses OpenAI when Singapore data is incomplete or unclear
- **⏰ Smart Intervals**: Checks at random intervals (5-15 minutes) to avoid overwhelming the server
- **🚨 Change Detection**: Only notifies when significant Singapore updates are detected
- **💾 Data Persistence**: Saves Singapore data to JSON files for comparison
- **🛡️ Robust Error Handling**: Continues monitoring even if individual checks fail

## Usage

### One-time Singapore Data Check
```bash
python yuuri.py
```

### Continuous Singapore Monitoring
```bash
python yuuri.py monitor
```

## Setup

1. **Install Dependencies**:
   ```bash
   pip install requests beautifulsoup4 openai
   ```

2. **Set OpenAI API Key** (optional, for intelligent fallback):
   ```bash
   export OPENAI_API_KEY='your-api-key-here'
   ```

## How It Works

### Normal Operation
1. Fetches website content using requests
2. Parses HTML with BeautifulSoup
3. Extracts Singapore concert information using predefined patterns
4. Uses Hong Kong data as reference template for complete data structure
5. Compares with previous Singapore data to detect changes

### Intelligent Fallback (OpenAI Integration)
When Singapore data is incomplete or "coming soon":
1. Automatically detects incomplete Singapore data
2. Sends HTML content to OpenAI GPT-4o-mini
3. AI analyzes content focusing specifically on Singapore updates
4. Returns structured Singapore data in consistent format
5. Marks data as complete/incomplete based on available details

### Monitoring Loop
- Runs continuously with random intervals (5-15 minutes)
- Focuses only on Singapore concert changes:
  - Concert dates and times
  - Venue information
  - Ticket prices and availability
  - Booking status updates
  - General announcement changes

## Output Files

- `singapore_concert_data.json`: Latest Singapore concert data
- `last_change.txt`: Timestamp of last detected Singapore change

## Example Output

```
🎵 Starting YUURI Asia Tour 2025 Singapore Monitor
🇸🇬 Focusing on Singapore concert updates only
==================================================

🔍 Check #1 at 2025-07-25 14:11:38
⚠️  Singapore data incomplete, using OpenAI for better analysis...
✅ Successfully analyzed with OpenAI
🚨 CHANGES DETECTED!

📊 SINGAPORE UPDATE:
============================================================
🇸🇬 YUURI ASIA TOUR 2025 - SINGAPORE UPDATE
============================================================
📊 Data source: Openai Analysis
📍 Venue: The Star Theatre
📅 Date: 27 September 2025
⏰ Time: Doors open 7pm, Show starts 8pm
🎫 Tickets: More details coming soon!
📋 Status: Details pending

💰 TICKET PRICES: Not yet announced

⏳ PENDING: Singapore details still being finalized
============================================================

😴 Sleeping for 8 minutes 23 seconds...
```

## Error Handling

- Network errors: Retries after 5 minutes
- Parsing errors: Falls back to OpenAI analysis
- OpenAI errors: Continues with normal parsing only
- Keyboard interrupt: Graceful shutdown

## Cost Considerations

- OpenAI API calls are only made when normal parsing fails
- Uses cost-effective GPT-4o-mini model
- Limits content sent to API to avoid excessive token usage
