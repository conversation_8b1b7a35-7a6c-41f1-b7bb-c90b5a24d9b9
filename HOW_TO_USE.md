# 🤖 How to Use Your YUURI Singapore Telegram Bot

Your Telegram bot is ready! Here's how to use it:

## 🚀 Starting the Bot

### Option 1: Use the startup script
```bash
./start_telegram_bot.sh
```

### Option 2: Manual start
```bash
export TELEGRAM_BOT_TOKEN='**********************************************'
export OPENAI_API_KEY='********************************************************************************************************************************************************************'
python telegram_bot.py
```

## 📱 Using the Bot in Telegram

### 1. Find Your Bot
- Search for your bot username in Telegram
- Or use the bot link that @BotFather gave you

### 2. Start Chatting
Send these commands to your bot:

| Command | What it does |
|---------|-------------|
| `/start` | Welcome message and introduction |
| `/subscribe` | **Subscribe to Singapore concert notifications** |
| `/status` | Check current Singapore concert information |
| `/check` | Force an immediate update check |
| `/unsubscribe` | Stop receiving notifications |
| `/help` | Show help message |

### 3. Get Notifications
Once you `/subscribe`, you'll automatically receive messages like this when Singapore concert details are updated:

```
🚨 SINGAPORE CONCERT UPDATE!

📊 Source: Openai Analysis
📍 Venue: The Star Theatre
📅 Date: 27 September 2025
⏰ Time: Doors open 7pm, Show starts 8pm
🎫 Tickets: Now available for booking!
📋 Status: Tickets on sale

💰 Ticket Prices:
• VIP Zone: SGD $180 (Available)
• Zone A: SGD $120 (Available)
• Zone B: SGD $80 (Available)

🎉 COMPLETE DATA: All Singapore details now available!
```

## 🔄 How It Works

1. **Automatic Monitoring**: Bot checks the YUURI website every 5-15 minutes
2. **Smart Detection**: Only sends notifications when there are actual changes
3. **AI-Powered**: Uses OpenAI when Singapore data is unclear or incomplete
4. **Multi-User**: Multiple people can subscribe to the same bot

## 🎯 What Triggers Notifications

The bot will notify you when Singapore concert details change:
- ✅ Ticket prices announced
- ✅ Venue details updated
- ✅ Show times confirmed
- ✅ Booking status changes
- ✅ Any significant announcements

## 🛠️ Running in Background

To keep the bot running even when you close the terminal:

### On Mac/Linux:
```bash
# Using nohup
nohup ./start_telegram_bot.sh > bot.log 2>&1 &

# Using screen
screen -S yuuri_bot ./start_telegram_bot.sh
```

### On Windows:
- Use Windows Task Scheduler
- Or run in a separate command prompt window

## 📊 Bot Status

When the bot is running, you'll see logs like:
```
2025-07-25 14:21:19,651 - __main__ - INFO - Starting YUURI Singapore Telegram Bot
2025-07-25 14:21:24,738 - telegram.ext.Application - INFO - Application started
```

This means your bot is successfully connected and ready to receive commands!

## 🔧 Troubleshooting

### Bot not responding?
1. Check if the bot process is still running
2. Restart with `./start_telegram_bot.sh`
3. Check the logs for error messages

### Not getting notifications?
1. Make sure you've sent `/subscribe` to the bot
2. Use `/check` to force an update check
3. Verify the bot is monitoring (check logs)

### Want to test it?
1. Send `/status` to see current Singapore info
2. Send `/check` to force an immediate check
3. The bot will tell you if changes are detected

## 🎵 Ready to Monitor!

Your bot is now ready to monitor YUURI's Singapore concert updates! Just:

1. **Start the bot**: `./start_telegram_bot.sh`
2. **Find your bot in Telegram**
3. **Send `/subscribe`**
4. **Wait for notifications!**

The bot will automatically notify you whenever there are Singapore concert updates. No more manual checking needed! 🎉
