# 📅 Daily Monitoring Update

Your YUURI Singapore Telegram Bot has been updated to use **daily check-ins** instead of continuous monitoring!

## 🔄 New Monitoring Schedule

### Daily Check-ins
- **Frequency**: Once per day at random times
- **Time Window**: 6:00 AM - 10:00 PM
- **Random Timing**: Each check happens at a different random time within the window
- **Sleep Duration**: 20-28 hours between checks (ensures daily coverage)

### What Happens Each Day

1. **Daily Status Update**: Channel receives a status update regardless of changes
2. **Change Detection**: If significant changes are detected, additional alerts are sent
3. **Smart Scheduling**: Next check is automatically scheduled for the following day

## 📢 Channel Notifications

### Daily Check-in Message
```
📅 DAILY CHECK-IN - 2025-07-25 14:30

🇸🇬 YUURI Singapore Concert Status
📊 Source: Normal Parsing
📍 Venue: The Star Theatre
📅 Date: 27 September 2025
⏰ Time: Doors open 7pm, Show starts 8pm
🎫 Tickets: More details coming soon!
📋 Status: Details pending

💰 Ticket Prices: Not yet announced

⏳ PENDING: Details still being finalized

🤖 Daily automated check - Next check in ~24 hours
```

### Change Alert (when updates detected)
```
🚨 SINGAPORE CONCERT UPDATE!

📊 Source: Openai Analysis
📍 Venue: The Star Theatre
📅 Date: 27 September 2025
⏰ Time: Doors open 7pm, Show starts 8pm
🎫 Tickets: Now available for booking!
📋 Status: Tickets on sale

💰 Ticket Prices:
• VIP Zone: SGD $180 (Available)
• Zone A: SGD $120 (Available)

🎉 COMPLETE DATA: All Singapore details now available!

🤖 Automated update from YUURI Singapore Monitor Bot
```

## 🤖 New Bot Commands

| Command | Description |
|---------|-------------|
| `/schedule` | View daily monitoring schedule and status |
| `/check` | Force an immediate check (still available) |
| `/status` | Check current Singapore concert info |
| `/testchannel` | Test channel messaging |

## 🎯 Benefits of Daily Monitoring

### ✅ Advantages
- **Less spam**: No frequent checks that might overwhelm
- **Daily updates**: Regular status updates keep you informed
- **Random timing**: Unpredictable check times
- **Change alerts**: Still get immediate notifications when changes happen
- **Resource efficient**: Lower API usage and server load

### 📊 Monitoring Behavior
- **Daily status**: Always sent to channel (even if no changes)
- **Change alerts**: Only sent when significant updates detected
- **Smart scheduling**: Automatically schedules next check
- **Error handling**: Retries on failures with 1-hour delays

## 🚀 How to Start

Same as before - just run:
```bash
./start_telegram_bot.sh
```

## 📱 User Experience

### For Subscribers
- Daily status updates in channel
- Immediate alerts when changes happen
- Use `/schedule` to check monitoring status
- Use `/check` to force immediate updates

### Sample Schedule Command Response
```
📅 Daily Monitoring Schedule

✅ Status: Active
🕐 Frequency: Once per day at random times
⏰ Time Window: 6:00 AM - 10:00 PM
📢 Channel Updates: Daily status sent to channel
🚨 Change Alerts: Immediate notifications when details change

The next check will happen within the next 24 hours at a random time.
```

## 🔧 Technical Details

### Scheduling Logic
```python
# Random time between 6 AM and 10 PM (18 hour window)
hours_from_now = random.randint(6, 22)
minutes_offset = random.randint(0, 59)

# Calculate sleep time (20-28 hours)
base_sleep = 24 * 60 * 60  # 24 hours
sleep_time = base_sleep + random_offset
```

### Monitoring Flow
1. **Sleep**: Wait for scheduled time (20-28 hours)
2. **Check**: Fetch Singapore concert data
3. **Daily Update**: Send status to channel
4. **Change Detection**: Compare with previous data
5. **Alerts**: Send notifications if changes detected
6. **Schedule**: Calculate next check time
7. **Repeat**: Loop back to step 1

## 🎵 Ready for Daily Monitoring!

Your bot now provides:
- ✅ Daily status updates at random times
- ✅ Immediate change alerts when needed
- ✅ Efficient resource usage
- ✅ Consistent daily coverage
- ✅ Channel notifications for all updates

The bot will keep you informed with daily check-ins while being respectful of server resources! 📅🤖
