#!/bin/bash

echo "🤖 YUURI Singapore Telegram Bot Setup"
echo "====================================="

# Check if bot token is set
if [ -z "$TELEGRAM_BOT_TOKEN" ]; then
    echo "❌ TELEGRAM_BOT_TOKEN not found!"
    echo ""
    echo "📱 To create a Telegram bot:"
    echo "1. Message @BotFather on Telegram"
    echo "2. Send /newbot"
    echo "3. Follow the instructions to create your bot"
    echo "4. Copy the bot token"
    echo "5. Set it with: export TELEGRAM_BOT_TOKEN='your-bot-token'"
    echo ""
    echo "Example:"
    echo "export TELEGRAM_BOT_TOKEN='123456789:ABCdefGHIjklMNOpqrsTUVwxyz'"
    echo ""
    exit 1
else
    echo "✅ TELEGRAM_BOT_TOKEN is set"
fi

# Check OpenAI API key
if [ -z "$OPENAI_API_KEY" ]; then
    echo "⚠️  OPENAI_API_KEY not set (optional but recommended)"
    echo "   For intelligent parsing: export OPENAI_API_KEY='your-openai-key'"
else
    echo "✅ OPENAI_API_KEY is set"
fi

# Check dependencies
echo ""
echo "📦 Checking dependencies..."
python3 -c "import telegram; print('✅ python-telegram-bot')" 2>/dev/null || echo "❌ python-telegram-bot - run: pip install python-telegram-bot"
python3 -c "import requests; print('✅ requests')" 2>/dev/null || echo "❌ requests - run: pip install requests"
python3 -c "import bs4; print('✅ beautifulsoup4')" 2>/dev/null || echo "❌ beautifulsoup4 - run: pip install beautifulsoup4"
python3 -c "import openai; print('✅ openai')" 2>/dev/null || echo "❌ openai - run: pip install openai"

echo ""
echo "🚀 Ready to start the bot!"
echo ""
echo "Commands:"
echo "  python telegram_bot.py  - Start the bot"
echo ""
echo "Bot Commands (in Telegram):"
echo "  /start      - Welcome message"
echo "  /subscribe  - Get Singapore concert notifications"
echo "  /status     - Check current Singapore concert info"
echo "  /check      - Force check for updates"
echo "  /help       - Show help"
echo ""
echo "🎯 The bot will monitor Singapore concert updates and notify subscribers!"
