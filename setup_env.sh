#!/bin/bash

# Setup script for YUURI monitor
echo "🎵 YUURI Asia Tour 2025 Monitor Setup"
echo "===================================="

# Check if OpenAI API key is set
if [ -z "$OPENAI_API_KEY" ]; then
    echo "⚠️  OpenAI API key not found in environment"
    echo ""
    echo "To enable intelligent fallback parsing, set your OpenAI API key:"
    echo "export OPENAI_API_KEY='********************************************************************************************************************************************************************'
    echo ""
    echo "You can add this to your ~/.bashrc or ~/.zshrc for persistence"
    echo ""
    echo "The monitor will still work without it, but won't have AI fallback"
else
    echo "✅ OpenAI API key is configured"
fi

# Check Python dependencies
echo ""
echo "📦 Checking dependencies..."

python3 -c "import requests; print('✅ requests')" 2>/dev/null || echo "❌ requests - run: pip install requests"
python3 -c "import bs4; print('✅ beautifulsoup4')" 2>/dev/null || echo "❌ beautifulsoup4 - run: pip install beautifulsoup4"
python3 -c "import openai; print('✅ openai')" 2>/dev/null || echo "❌ openai - run: pip install openai"

echo ""
echo "🚀 Ready to run!"
echo "   One-time check: python yuuri.py"
echo "   Continuous monitor: python yuuri.py monitor"
