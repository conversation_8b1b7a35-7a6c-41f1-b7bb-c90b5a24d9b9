#!/bin/bash

# YUURI Singapore Concert Monitor Startup Script
echo "🎵 YUURI Asia Tour 2025 Singapore Monitor"
echo "========================================="

# Set OpenAI API key (replace with your actual key)
export OPENAI_API_KEY='********************************************************************************************************************************************************************'

echo "🚀 Starting Singapore concert monitoring..."
echo "🔄 Will check for updates every 5-15 minutes"
echo "🤖 Using OpenAI for intelligent parsing when needed"
echo "📱 Press Ctrl+C to stop monitoring"
echo ""

# Start the monitor
python yuuri.py monitor
