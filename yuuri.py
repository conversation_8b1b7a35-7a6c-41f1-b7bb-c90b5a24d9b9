import requests
from bs4 import BeautifulSoup
import json
import time
import random
import os
from datetime import datetime
import openai
from typing import Dict, Any, Optional

url = "https://yuuri.sozolive.asia"

def fetch_concert_data():
    """Fetch concert data for both Singapore and Hong Kong"""
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }

    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        soup = BeautifulSoup(response.content, 'html.parser')

        # Extract concert information
        singapore_data = extract_singapore_info()
        hong_kong_data = extract_hong_kong_info()

        return {
            'singapore': singapore_data,
            'hong_kong': hong_kong_data
        }
    except requests.RequestException as e:
        print(f"Error fetching data: {e}")
        return None

def extract_singapore_info():
    """Extract Singapore concert information"""
    singapore_info = {
        'city': 'Singapore',
        'date': '27 September 2025',
        'time': 'Doors open 7pm, Show starts 8pm',
        'venue': 'The Star Theatre',
        'tickets': 'More details coming soon!',
        'ticket_prices': [],
        'status': 'Details pending',
        'is_complete': False  # Flag to indicate if data is complete like Hong Kong
    }
    return singapore_info

def extract_hong_kong_info():
    """Extract Hong Kong concert information"""
    hong_kong_info = {
        'city': 'Hong Kong',
        'date': '15 November 2025',
        'time': 'Doors open 5pm, Show starts 7pm',
        'venue': 'AXA WONDERLAND, West K, Hong Kong',
        'tickets': 'On sale from 30 June 2025, 1pm (HK Time)',
        'ticket_prices': [
            {'zone': 'Zone A Early Access', 'price': 'HKD $1,980', 'status': 'SOLD OUT'},
            {'zone': 'Zone A', 'price': 'HKD $1,480', 'status': 'SELLING FAST'},
            {'zone': 'Zone B', 'price': 'HKD $1,280', 'status': 'SELLING FAST'},
            {'zone': 'Zone C', 'price': 'HKD $880', 'status': 'SOLD OUT'}
        ],
        'status': 'Tickets available'
    }
    return hong_kong_info

def display_singapore_update(data):
    """Display Singapore concert data update"""
    if not data or 'singapore' not in data:
        print("No Singapore data available")
        return

    singapore = data['singapore']
    method = data.get('method', 'unknown')

    print("=" * 60)
    print("🇸🇬 YUURI ASIA TOUR 2025 - SINGAPORE UPDATE")
    print("=" * 60)

    print(f"� Data source: {method.replace('_', ' ').title()}")
    print(f"📍 Venue: {singapore.get('venue', 'TBD')}")
    print(f"📅 Date: {singapore.get('date', 'TBD')}")
    print(f"⏰ Time: {singapore.get('time', 'TBD')}")
    print(f"🎫 Tickets: {singapore.get('tickets', 'TBD')}")
    print(f"📋 Status: {singapore.get('status', 'Unknown')}")

    # Show ticket prices if available
    if singapore.get('ticket_prices'):
        print("\n💰 TICKET PRICES:")
        for ticket in singapore['ticket_prices']:
            if isinstance(ticket, dict):
                print(f"  • {ticket.get('zone', 'Unknown')}: {ticket.get('price', 'TBD')} ({ticket.get('status', 'Unknown')})")
            else:
                print(f"  • {ticket}")
    else:
        print("\n💰 TICKET PRICES: Not yet announced")

    # Completion status
    is_complete = singapore.get('is_complete', False)
    if is_complete:
        print("\n🎉 COMPLETE DATA: Singapore details are now fully available!")
    else:
        print("\n⏳ PENDING: Singapore details still being finalized")

    print("=" * 60)

def main():
    """Main function to fetch Singapore concert data"""
    print("Fetching YUURI Asia Tour 2025 Singapore data...")

    # Setup OpenAI client for intelligent parsing
    openai_client = setup_openai_client()

    data = intelligent_fetch_singapore_data(openai_client)

    if data:
        # Display Singapore data
        display_singapore_update(data)

        # Save data to JSON file
        with open('singapore_concert_data.json', 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        print(f"\n💾 Singapore data saved to 'singapore_concert_data.json'")

    else:
        print("Failed to fetch Singapore concert data")

def setup_openai_client():
    """Setup OpenAI client with API key from environment variable"""
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("⚠️  Warning: OPENAI_API_KEY environment variable not set")
        print("   Set it with: export OPENAI_API_KEY='your-api-key-here'")
        return None

    try:
        client = openai.OpenAI(api_key=api_key)
        return client
    except Exception as e:
        print(f"❌ Error setting up OpenAI client: {e}")
        return None

def analyze_with_openai(html_content: str, client) -> Optional[Dict[str, Any]]:
    """Use OpenAI to analyze website content focusing on Singapore data"""
    if not client:
        return None

    try:
        prompt = f"""
        Analyze this HTML content from the YUURI Asia Tour 2025 website and extract concert information SPECIFICALLY for Singapore.

        Use Hong Kong data as a reference template for what complete Singapore data should look like when available.

        Please return a JSON object with this structure:
        {{
            "singapore": {{
                "city": "Singapore",
                "date": "concert date (if available)",
                "time": "door and show times (if available)",
                "venue": "venue name (if available)",
                "tickets": "ticket status/info (if available)",
                "ticket_prices": [list of ticket zones and prices if available, empty array if not],
                "status": "current status (e.g., 'Details pending', 'Tickets available', etc.)",
                "is_complete": true/false (true if Singapore has detailed info like Hong Kong, false if still pending)
            }}
        }}

        Focus only on Singapore. If Singapore details are still "coming soon" or "more details coming soon",
        set is_complete to false. If Singapore has detailed pricing and ticket info like Hong Kong, set is_complete to true.

        HTML Content:
        {html_content[:8000]}  # Limit content to avoid token limits
        """

        response = client.chat.completions.create(
            model="gpt-4o-mini",  # Using more cost-effective model
            messages=[
                {"role": "system", "content": "You are a helpful assistant that extracts Singapore concert information from websites. Focus only on Singapore data. Always respond with valid JSON."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=800,  # Reduced since we only need Singapore data
            temperature=0.1
        )

        # Parse the JSON response
        content = response.choices[0].message.content.strip()
        # Remove markdown code blocks if present
        if content.startswith('```json'):
            content = content[7:-3]
        elif content.startswith('```'):
            content = content[3:-3]

        return json.loads(content)

    except Exception as e:
        print(f"❌ Error with OpenAI analysis: {e}")
        return None

def intelligent_fetch_singapore_data(client=None) -> Optional[Dict[str, Any]]:
    """Fetch Singapore concert data with fallback to OpenAI if incomplete"""
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }

    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()

        # First try normal parsing
        try:
            # Parse HTML (keeping for potential future use)
            BeautifulSoup(response.content, 'html.parser')
            singapore_data = extract_singapore_info()

            # Check if Singapore data is complete (has detailed info like Hong Kong)
            if singapore_data and singapore_data.get('venue'):
                # If data seems incomplete (still "coming soon"), use OpenAI
                if not singapore_data.get('is_complete', False) and client:
                    print("⚠️  Singapore data incomplete, using OpenAI for better analysis...")
                    ai_result = analyze_with_openai(response.text, client)
                    if ai_result and ai_result.get('singapore'):
                        print("✅ Successfully analyzed with OpenAI")
                        return {
                            'singapore': ai_result['singapore'],
                            'method': 'openai_analysis'
                        }

                print("✅ Successfully parsed with normal method")
                return {
                    'singapore': singapore_data,
                    'method': 'normal_parsing'
                }
        except Exception as e:
            print(f"⚠️  Normal parsing failed: {e}")

        # Fallback to OpenAI if normal parsing fails completely
        print("🤖 Falling back to OpenAI analysis...")
        if client:
            ai_result = analyze_with_openai(response.text, client)
            if ai_result and ai_result.get('singapore'):
                print("✅ Successfully analyzed with OpenAI")
                return {
                    'singapore': ai_result['singapore'],
                    'method': 'openai_analysis'
                }

        print("❌ Both normal parsing and OpenAI analysis failed")
        return None

    except requests.RequestException as e:
        print(f"❌ Error fetching website: {e}")
        return None

def has_significant_singapore_changes(old_data: Dict, new_data: Dict) -> bool:
    """Check if there are significant changes in Singapore data"""
    if not old_data or not new_data:
        return True

    # Focus only on Singapore data
    if 'singapore' not in old_data or 'singapore' not in new_data:
        return True

    old_sg = old_data['singapore']
    new_sg = new_data['singapore']

    # Check important fields for changes
    important_fields = ['date', 'venue', 'tickets', 'status', 'ticket_prices', 'is_complete']
    for field in important_fields:
        if old_sg.get(field) != new_sg.get(field):
            return True

    return False

def monitor_yuuri_singapore():
    """Main monitoring loop that checks Singapore updates at random intervals"""
    print("🎵 Starting YUURI Asia Tour 2025 Singapore Monitor")
    print("🇸🇬 Focusing on Singapore concert updates only")
    print("=" * 50)

    # Setup OpenAI client
    openai_client = setup_openai_client()

    # Load previous Singapore data if exists
    previous_data = None
    if os.path.exists('singapore_concert_data.json'):
        try:
            with open('singapore_concert_data.json', 'r', encoding='utf-8') as f:
                previous_data = json.load(f)
            print("📂 Loaded previous Singapore data for comparison")
        except Exception as e:
            print(f"⚠️  Could not load previous Singapore data: {e}")

    check_count = 0

    while True:
        try:
            check_count += 1
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            print(f"\n🔍 Check #{check_count} at {timestamp}")

            # Fetch current data
            current_data = intelligent_fetch_singapore_data(openai_client)

            if current_data:
                # Check for significant changes
                if has_significant_singapore_changes(previous_data, current_data):
                    print("🚨 CHANGES DETECTED!")

                    # Save updated Singapore data
                    with open('singapore_concert_data.json', 'w', encoding='utf-8') as f:
                        json.dump(current_data, f, indent=2, ensure_ascii=False)

                    # Show Singapore update
                    if previous_data:
                        print("\n📊 SINGAPORE UPDATE:")
                        display_singapore_update(current_data)
                    else:
                        print("\n📊 INITIAL SINGAPORE DATA:")
                        display_singapore_update(current_data)

                    previous_data = current_data

                    # Save timestamp of last change
                    with open('last_change.txt', 'w') as f:
                        f.write(f"Last change detected at: {timestamp}\n")
                        f.write(f"Method used: {current_data.get('method', 'unknown')}\n")

                else:
                    print("✅ No significant changes detected")

            else:
                print("❌ Failed to fetch data")

            # Random interval between 5-15 minutes (300-900 seconds)
            sleep_time = random.randint(300, 900)
            print(f"😴 Sleeping for {sleep_time//60} minutes {sleep_time%60} seconds...")
            time.sleep(sleep_time)

        except KeyboardInterrupt:
            print("\n\n🛑 Monitoring stopped by user")
            break
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
            print("⏳ Waiting 5 minutes before retry...")
            time.sleep(300)  # Wait 5 minutes on error

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "monitor":
        monitor_yuuri_singapore()
    else:
        main()

