import requests
from bs4 import BeautifulSoup
import json

url = "https://yuuri.sozolive.asia"

def fetch_concert_data():
    """Fetch concert data for both Singapore and Hong Kong"""
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }

    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        soup = BeautifulSoup(response.content, 'html.parser')

        # Extract concert information
        singapore_data = extract_singapore_info()
        hong_kong_data = extract_hong_kong_info()

        return {
            'singapore': singapore_data,
            'hong_kong': hong_kong_data
        }
    except requests.RequestException as e:
        print(f"Error fetching data: {e}")
        return None

def extract_singapore_info():
    """Extract Singapore concert information"""
    singapore_info = {
        'city': 'Singapore',
        'date': '27 September 2025',
        'time': 'Doors open 7pm, Show starts 8pm',
        'venue': 'The Star Theatre',
        'tickets': 'More details coming soon!',
        'ticket_prices': [],
        'status': 'Details pending'
    }
    return singapore_info

def extract_hong_kong_info(soup):
    """Extract Hong Kong concert information"""
    hong_kong_info = {
        'city': 'Hong Kong',
        'date': '15 November 2025',
        'time': 'Doors open 5pm, Show starts 7pm',
        'venue': 'AXA WONDERLAND, West K, Hong Kong',
        'tickets': 'On sale from 30 June 2025, 1pm (HK Time)',
        'ticket_prices': [
            {'zone': 'Zone A Early Access', 'price': 'HKD $1,980', 'status': 'SOLD OUT'},
            {'zone': 'Zone A', 'price': 'HKD $1,480', 'status': 'SELLING FAST'},
            {'zone': 'Zone B', 'price': 'HKD $1,280', 'status': 'SELLING FAST'},
            {'zone': 'Zone C', 'price': 'HKD $880', 'status': 'SOLD OUT'}
        ],
        'status': 'Tickets available'
    }
    return hong_kong_info

def compare_concerts(data):
    """Compare Singapore and Hong Kong concert data"""
    if not data:
        print("No data available for comparison")
        return

    singapore = data['singapore']
    hong_kong = data['hong_kong']

    print("=" * 60)
    print("YUURI ASIA TOUR 2025 - SINGAPORE vs HONG KONG COMPARISON")
    print("=" * 60)

    # Basic info comparison
    print("\n📍 VENUE COMPARISON:")
    print(f"Singapore: {singapore['venue']}")
    print(f"Hong Kong: {hong_kong['venue']}")

    print("\n📅 DATE & TIME COMPARISON:")
    print(f"Singapore: {singapore['date']} | {singapore['time']}")
    print(f"Hong Kong: {hong_kong['date']} | {hong_kong['time']}")

    print("\n🎫 TICKET STATUS:")
    print(f"Singapore: {singapore['status']} - {singapore['tickets']}")
    print(f"Hong Kong: {hong_kong['status']} - {hong_kong['tickets']}")

    # Ticket prices comparison
    print("\n💰 TICKET PRICES:")
    print("Singapore: No pricing information available yet")
    print("Hong Kong:")
    for ticket in hong_kong['ticket_prices']:
        print(f"  • {ticket['zone']}: {ticket['price']} ({ticket['status']})")

    # Key differences
    print("\n🔍 KEY DIFFERENCES:")
    print("1. Hong Kong concert is earlier (September vs November)")
    print("2. Hong Kong has detailed pricing and ticket sales active")
    print("3. Singapore details are still pending announcement")
    print("4. Hong Kong show starts earlier (7pm vs 8pm)")
    print("5. Hong Kong tickets are selling fast/sold out, indicating high demand")

def main():
    """Main function to fetch and compare concert data"""
    print("Fetching YUURI Asia Tour 2025 data...")
    data = fetch_concert_data()

    if data:
        # Display individual concert info
        print("\n" + "=" * 40)
        print("SINGAPORE CONCERT INFO")
        print("=" * 40)
        singapore = data['singapore']
        for key, value in singapore.items():
            if key != 'ticket_prices':
                print(f"{key.replace('_', ' ').title()}: {value}")

        print("\n" + "=" * 40)
        print("HONG KONG CONCERT INFO")
        print("=" * 40)
        hong_kong = data['hong_kong']
        for key, value in hong_kong.items():
            if key == 'ticket_prices':
                print("Ticket Prices:")
                for ticket in value:
                    print(f"  • {ticket['zone']}: {ticket['price']} ({ticket['status']})")
            else:
                print(f"{key.replace('_', ' ').title()}: {value}")

        # Compare the data
        compare_concerts(data)

        # Save data to JSON file
        with open('yuuri_concert_data.json', 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        print(f"\n💾 Data saved to 'yuuri_concert_data.json'")

    else:
        print("Failed to fetch concert data")

if __name__ == "__main__":
    main()

