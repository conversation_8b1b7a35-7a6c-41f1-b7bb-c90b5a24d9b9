import requests
from bs4 import BeautifulSoup
import json
import time
import random
import os
from datetime import datetime
import openai
from typing import Dict, Any, Optional

url = "https://yuuri.sozolive.asia"

def fetch_concert_data():
    """Fetch concert data for both Singapore and Hong Kong"""
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }

    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        soup = BeautifulSoup(response.content, 'html.parser')

        # Extract concert information
        singapore_data = extract_singapore_info()
        hong_kong_data = extract_hong_kong_info()

        return {
            'singapore': singapore_data,
            'hong_kong': hong_kong_data
        }
    except requests.RequestException as e:
        print(f"Error fetching data: {e}")
        return None

def extract_singapore_info():
    """Extract Singapore concert information"""
    singapore_info = {
        'city': 'Singapore',
        'date': '27 September 2025',
        'time': 'Doors open 7pm, Show starts 8pm',
        'venue': 'The Star Theatre',
        'tickets': 'More details coming soon!',
        'ticket_prices': [],
        'status': 'Details pending'
    }
    return singapore_info

def extract_hong_kong_info():
    """Extract Hong Kong concert information"""
    hong_kong_info = {
        'city': 'Hong Kong',
        'date': '15 November 2025',
        'time': 'Doors open 5pm, Show starts 7pm',
        'venue': 'AXA WONDERLAND, West K, Hong Kong',
        'tickets': 'On sale from 30 June 2025, 1pm (HK Time)',
        'ticket_prices': [
            {'zone': 'Zone A Early Access', 'price': 'HKD $1,980', 'status': 'SOLD OUT'},
            {'zone': 'Zone A', 'price': 'HKD $1,480', 'status': 'SELLING FAST'},
            {'zone': 'Zone B', 'price': 'HKD $1,280', 'status': 'SELLING FAST'},
            {'zone': 'Zone C', 'price': 'HKD $880', 'status': 'SOLD OUT'}
        ],
        'status': 'Tickets available'
    }
    return hong_kong_info

def compare_concerts(data):
    """Compare Singapore and Hong Kong concert data"""
    if not data:
        print("No data available for comparison")
        return

    singapore = data['singapore']
    hong_kong = data['hong_kong']

    print("=" * 60)
    print("YUURI ASIA TOUR 2025 - SINGAPORE vs HONG KONG COMPARISON")
    print("=" * 60)

    # Basic info comparison
    print("\n📍 VENUE COMPARISON:")
    print(f"Singapore: {singapore['venue']}")
    print(f"Hong Kong: {hong_kong['venue']}")

    print("\n📅 DATE & TIME COMPARISON:")
    print(f"Singapore: {singapore['date']} | {singapore['time']}")
    print(f"Hong Kong: {hong_kong['date']} | {hong_kong['time']}")

    print("\n🎫 TICKET STATUS:")
    print(f"Singapore: {singapore['status']} - {singapore['tickets']}")
    print(f"Hong Kong: {hong_kong['status']} - {hong_kong['tickets']}")

    # Ticket prices comparison
    print("\n💰 TICKET PRICES:")
    print("Singapore: No pricing information available yet")
    print("Hong Kong:")
    for ticket in hong_kong['ticket_prices']:
        print(f"  • {ticket['zone']}: {ticket['price']} ({ticket['status']})")

    # Key differences
    print("\n🔍 KEY DIFFERENCES:")
    print("1. Hong Kong concert is earlier (September vs November)")
    print("2. Hong Kong has detailed pricing and ticket sales active")
    print("3. Singapore details are still pending announcement")
    print("4. Hong Kong show starts earlier (7pm vs 8pm)")
    print("5. Hong Kong tickets are selling fast/sold out, indicating high demand")

def main():
    """Main function to fetch and compare concert data"""
    print("Fetching YUURI Asia Tour 2025 data...")
    data = fetch_concert_data()

    if data:
        # Display individual concert info
        print("\n" + "=" * 40)
        print("SINGAPORE CONCERT INFO")
        print("=" * 40)
        singapore = data['singapore']
        for key, value in singapore.items():
            if key != 'ticket_prices':
                print(f"{key.replace('_', ' ').title()}: {value}")

        print("\n" + "=" * 40)
        print("HONG KONG CONCERT INFO")
        print("=" * 40)
        hong_kong = data['hong_kong']
        for key, value in hong_kong.items():
            if key == 'ticket_prices':
                print("Ticket Prices:")
                for ticket in value:
                    print(f"  • {ticket['zone']}: {ticket['price']} ({ticket['status']})")
            else:
                print(f"{key.replace('_', ' ').title()}: {value}")

        # Compare the data
        compare_concerts(data)

        # Save data to JSON file
        with open('yuuri_concert_data.json', 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        print(f"\n💾 Data saved to 'yuuri_concert_data.json'")

    else:
        print("Failed to fetch concert data")

def setup_openai_client():
    """Setup OpenAI client with API key from environment variable"""
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("⚠️  Warning: OPENAI_API_KEY environment variable not set")
        print("   Set it with: export OPENAI_API_KEY='your-api-key-here'")
        return None

    try:
        client = openai.OpenAI(api_key=api_key)
        return client
    except Exception as e:
        print(f"❌ Error setting up OpenAI client: {e}")
        return None

def analyze_with_openai(html_content: str, client) -> Optional[Dict[str, Any]]:
    """Use OpenAI to analyze website content when normal parsing fails"""
    if not client:
        return None

    try:
        prompt = f"""
        Analyze this HTML content from the YUURI Asia Tour 2025 website and extract concert information for Singapore and Hong Kong.

        Please return a JSON object with the following structure:
        {{
            "singapore": {{
                "city": "Singapore",
                "date": "concert date",
                "time": "door and show times",
                "venue": "venue name",
                "tickets": "ticket status/info",
                "ticket_prices": [list of ticket zones and prices if available],
                "status": "current status"
            }},
            "hong_kong": {{
                "city": "Hong Kong",
                "date": "concert date",
                "time": "door and show times",
                "venue": "venue name",
                "tickets": "ticket status/info",
                "ticket_prices": [list of ticket zones and prices if available],
                "status": "current status"
            }}
        }}

        HTML Content:
        {html_content[:8000]}  # Limit content to avoid token limits
        """

        response = client.chat.completions.create(
            model="gpt-4o-mini",  # Using more cost-effective model
            messages=[
                {"role": "system", "content": "You are a helpful assistant that extracts concert information from websites. Always respond with valid JSON."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=1500,
            temperature=0.1
        )

        # Parse the JSON response
        content = response.choices[0].message.content.strip()
        # Remove markdown code blocks if present
        if content.startswith('```json'):
            content = content[7:-3]
        elif content.startswith('```'):
            content = content[3:-3]

        return json.loads(content)

    except Exception as e:
        print(f"❌ Error with OpenAI analysis: {e}")
        return None

def intelligent_fetch_concert_data(client=None) -> Optional[Dict[str, Any]]:
    """Fetch concert data with fallback to OpenAI if normal parsing fails"""
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }

    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()

        # First try normal parsing
        try:
            # Parse HTML (keeping for potential future use)
            BeautifulSoup(response.content, 'html.parser')
            singapore_data = extract_singapore_info()
            hong_kong_data = extract_hong_kong_info()

            # Check if we got meaningful data (basic validation)
            if (singapore_data and hong_kong_data and
                singapore_data.get('venue') and hong_kong_data.get('venue')):
                print("✅ Successfully parsed with normal method")
                return {
                    'singapore': singapore_data,
                    'hong_kong': hong_kong_data,
                    'method': 'normal_parsing'
                }
        except Exception as e:
            print(f"⚠️  Normal parsing failed: {e}")

        # Fallback to OpenAI if normal parsing fails or returns incomplete data
        print("🤖 Falling back to OpenAI analysis...")
        if client:
            ai_result = analyze_with_openai(response.text, client)
            if ai_result:
                ai_result['method'] = 'openai_analysis'
                print("✅ Successfully analyzed with OpenAI")
                return ai_result

        print("❌ Both normal parsing and OpenAI analysis failed")
        return None

    except requests.RequestException as e:
        print(f"❌ Error fetching website: {e}")
        return None

def has_significant_changes(old_data: Dict, new_data: Dict) -> bool:
    """Check if there are significant changes between old and new data"""
    if not old_data or not new_data:
        return True

    # Check for changes in key fields
    for city in ['singapore', 'hong_kong']:
        if city in old_data and city in new_data:
            old_city = old_data[city]
            new_city = new_data[city]

            # Check important fields for changes
            important_fields = ['date', 'venue', 'tickets', 'status', 'ticket_prices']
            for field in important_fields:
                if old_city.get(field) != new_city.get(field):
                    return True

    return False

def monitor_yuuri_concerts():
    """Main monitoring loop that checks for updates at random intervals"""
    print("🎵 Starting YUURI Asia Tour 2025 Monitor")
    print("=" * 50)

    # Setup OpenAI client
    openai_client = setup_openai_client()

    # Load previous data if exists
    previous_data = None
    if os.path.exists('yuuri_concert_data.json'):
        try:
            with open('yuuri_concert_data.json', 'r', encoding='utf-8') as f:
                previous_data = json.load(f)
            print("📂 Loaded previous data for comparison")
        except Exception as e:
            print(f"⚠️  Could not load previous data: {e}")

    check_count = 0

    while True:
        try:
            check_count += 1
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            print(f"\n🔍 Check #{check_count} at {timestamp}")

            # Fetch current data
            current_data = intelligent_fetch_concert_data(openai_client)

            if current_data:
                # Check for significant changes
                if has_significant_changes(previous_data, current_data):
                    print("🚨 CHANGES DETECTED!")

                    # Save updated data
                    with open('yuuri_concert_data.json', 'w', encoding='utf-8') as f:
                        json.dump(current_data, f, indent=2, ensure_ascii=False)

                    # Show comparison if we have previous data
                    if previous_data:
                        print("\n📊 COMPARISON:")
                        compare_concerts(current_data)
                    else:
                        print("\n📊 INITIAL DATA:")
                        compare_concerts(current_data)

                    previous_data = current_data

                    # Save timestamp of last change
                    with open('last_change.txt', 'w') as f:
                        f.write(f"Last change detected at: {timestamp}\n")
                        f.write(f"Method used: {current_data.get('method', 'unknown')}\n")

                else:
                    print("✅ No significant changes detected")

            else:
                print("❌ Failed to fetch data")

            # Random interval between 5-15 minutes (300-900 seconds)
            sleep_time = random.randint(300, 900)
            print(f"😴 Sleeping for {sleep_time//60} minutes {sleep_time%60} seconds...")
            time.sleep(sleep_time)

        except KeyboardInterrupt:
            print("\n\n🛑 Monitoring stopped by user")
            break
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
            print("⏳ Waiting 5 minutes before retry...")
            time.sleep(300)  # Wait 5 minutes on error

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "monitor":
        monitor_yuuri_concerts()
    else:
        main()

