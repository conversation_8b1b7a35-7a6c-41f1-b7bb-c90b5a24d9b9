#!/bin/bash

# YUURI Singapore Telegram Bot Startup Script
echo "🤖 Starting YUURI Singapore Telegram Bot"
echo "========================================"

# Set your bot token here (get from @BotFather)
export TELEGRAM_BOT_TOKEN='**********************************************'

# Set OpenAI API key for intelligent parsing
export OPENAI_API_KEY='********************************************************************************************************************************************************************'

echo "🇸🇬 Monitoring Singapore concert updates"
echo "📱 <PERSON><PERSON> will respond to Telegram commands"
echo "📢 Sending updates to channel: -4897398016"
echo "🔄 Automatic checks every 5-15 minutes"
echo "🤖 Using OpenAI for intelligent parsing"
echo ""
echo "📋 Available commands in Telegram:"
echo "   /start - Welcome message"
echo "   /subscribe - Get notifications"
echo "   /status - Check current info"
echo "   /check - Force update check"
echo "   /testchannel - Test channel messaging"
echo ""
echo "🛑 Press Ctrl+C to stop the bot"
echo ""

# Start the bot
python telegram_bot.py
