# 🤖 YUURI Singapore Telegram Bot

A Telegram bot that monitors the YUURI Asia Tour 2025 website specifically for **Singapore concert updates** and sends notifications directly to your Telegram.

## 🎯 Features

- **🇸🇬 Singapore-Focused**: Monitors only Singapore concert updates
- **📱 Telegram Notifications**: Get instant updates in your Telegram
- **🤖 AI-Powered**: Uses OpenAI for intelligent parsing when data is unclear
- **⏰ Smart Monitoring**: Checks every 5-15 minutes automatically
- **👥 Multi-User**: Multiple people can subscribe to the same bot
- **🔄 Real-Time**: Only notifies when actual changes are detected

## 🚀 Quick Setup

### 1. Create a Telegram Bot

1. Message [@BotFather](https://t.me/BotFather) on Telegram
2. Send `/newbot`
3. Choose a name for your bot (e.g., "YUURI Singapore Monitor")
4. Choose a username (e.g., "yuuri_singapore_bot")
5. Copy the bot token you receive

### 2. Set Environment Variables

```bash
# Required: Your bot token from BotFather
export TELEGRAM_BOT_TOKEN='123456789:ABCdefGHIjklMNOpqrsTUVwxyz'

# Optional but recommended: OpenAI API key for intelligent parsing
export OPENAI_API_KEY='your-openai-api-key'
```

### 3. Install Dependencies

```bash
pip install python-telegram-bot requests beautifulsoup4 openai
```

### 4. Start the Bot

```bash
python telegram_bot.py
```

Or use the convenience script:
```bash
./start_telegram_bot.sh
```

## 📱 Bot Commands

Once your bot is running, users can interact with it using these commands:

| Command | Description |
|---------|-------------|
| `/start` | Welcome message and bot introduction |
| `/subscribe` | Subscribe to Singapore concert notifications |
| `/unsubscribe` | Stop receiving notifications |
| `/status` | Check current Singapore concert information |
| `/check` | Force an immediate check for updates |
| `/help` | Show help message with all commands |

## 🔔 How Notifications Work

### What Triggers Notifications

The bot monitors for changes in:
- **Ticket prices** (when announced)
- **Venue details** (updates or confirmations)
- **Show times** (door times, start times)
- **Booking status** (when tickets go on sale)
- **General announcements** (any significant updates)

### Sample Notification

```
🚨 SINGAPORE CONCERT UPDATE!

📊 Source: Openai Analysis
📍 Venue: The Star Theatre
📅 Date: 27 September 2025
⏰ Time: Doors open 7pm, Show starts 8pm
🎫 Tickets: Now available!
📋 Status: Tickets on sale

💰 Ticket Prices:
• VIP Zone: SGD $180 (Available)
• Zone A: SGD $120 (Available)
• Zone B: SGD $80 (Available)

🎉 COMPLETE DATA: All Singapore details now available!
```

## 🤖 Intelligent Features

### Normal vs AI Parsing

1. **Normal Mode**: Extracts basic info using HTML parsing
2. **AI Mode**: When Singapore data is incomplete (like "More details coming soon"), automatically uses OpenAI to analyze the content more intelligently
3. **Smart Detection**: Determines when Singapore has complete information (like Hong Kong currently does)

### Cost Optimization

- OpenAI is only called when normal parsing fails or data is incomplete
- Uses cost-effective GPT-4o-mini model
- Limits content sent to API to control costs

## 📁 Files Created

- `subscribers.json` - List of subscribed chat IDs
- `singapore_concert_data.json` - Latest Singapore concert data
- Bot logs - Detailed logging for monitoring and debugging

## 🛠️ Advanced Usage

### Running in Background

```bash
# Using nohup (Linux/Mac)
nohup python telegram_bot.py > bot.log 2>&1 &

# Using screen (Linux/Mac)
screen -S yuuri_bot python telegram_bot.py

# Using systemd (Linux)
# Create a service file for automatic startup
```

### Multiple Bots

You can run multiple instances with different bot tokens for different groups or purposes.

### Customization

The bot code is modular and can be easily customized:
- Change monitoring intervals
- Modify notification messages
- Add new commands
- Integrate with other services

## 🔧 Troubleshooting

### Common Issues

1. **"TELEGRAM_BOT_TOKEN not set"**
   - Make sure you've exported the bot token correctly
   - Check the token format (should be like `123456789:ABC...`)

2. **"OpenAI API key not set"**
   - This is optional but recommended
   - Bot will work without it but with limited parsing capabilities

3. **Bot not responding**
   - Check if the bot is running (`python telegram_bot.py`)
   - Verify the bot token is correct
   - Check bot logs for errors

4. **No notifications received**
   - Make sure you've subscribed with `/subscribe`
   - Check if there are actual changes on the website
   - Use `/check` to force an update check

### Logs

The bot provides detailed logging. Check the console output for:
- Connection status
- Monitoring activity
- Error messages
- Subscriber activity

## 🔒 Security Notes

- Keep your bot token secure (don't share it publicly)
- The bot only stores chat IDs of subscribers
- No personal data is collected beyond what's needed for notifications
- OpenAI API calls only include website content, no user data

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Review the bot logs for error messages
3. Ensure all dependencies are installed correctly
4. Verify environment variables are set properly

## 🎵 About YUURI

This bot monitors updates for YUURI (優里), the popular Japanese singer-songwriter, specifically for his Singapore concert as part of the YUURI Asia Tour 2025.
