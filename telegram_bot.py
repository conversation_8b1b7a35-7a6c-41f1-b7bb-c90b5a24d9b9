import asyncio
import logging
import os
import json
import time
import random
from datetime import datetime
from typing import Dict, Any, Optional

import requests
from bs4 import BeautifulSoup
import openai
from telegram import Update
from telegram.ext import Application, CommandHandler, ContextTypes

# Import functions from yuuri.py
from yuuri import (
    intelligent_fetch_singapore_data, 
    setup_openai_client,
    has_significant_singapore_changes,
    display_singapore_update
)

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class YuuriTelegramBot:
    def __init__(self, bot_token: str):
        self.bot_token = bot_token
        self.application = Application.builder().token(bot_token).build()
        self.openai_client = setup_openai_client()
        self.subscribers = set()  # Store chat IDs of subscribers
        self.monitoring = False
        self.previous_data = None
        
        # Load previous data if exists
        self.load_previous_data()
        
        # Setup handlers
        self.setup_handlers()
    
    def load_previous_data(self):
        """Load previous Singapore data"""
        if os.path.exists('singapore_concert_data.json'):
            try:
                with open('singapore_concert_data.json', 'r', encoding='utf-8') as f:
                    self.previous_data = json.load(f)
                logger.info("Loaded previous Singapore data")
            except Exception as e:
                logger.error(f"Could not load previous data: {e}")
    
    def save_subscribers(self):
        """Save subscriber list to file"""
        try:
            with open('subscribers.json', 'w') as f:
                json.dump(list(self.subscribers), f)
        except Exception as e:
            logger.error(f"Could not save subscribers: {e}")
    
    def load_subscribers(self):
        """Load subscriber list from file"""
        try:
            if os.path.exists('subscribers.json'):
                with open('subscribers.json', 'r') as f:
                    self.subscribers = set(json.load(f))
                logger.info(f"Loaded {len(self.subscribers)} subscribers")
        except Exception as e:
            logger.error(f"Could not load subscribers: {e}")
    
    def setup_handlers(self):
        """Setup command handlers"""
        self.application.add_handler(CommandHandler("start", self.start_command))
        self.application.add_handler(CommandHandler("subscribe", self.subscribe_command))
        self.application.add_handler(CommandHandler("unsubscribe", self.unsubscribe_command))
        self.application.add_handler(CommandHandler("status", self.status_command))
        self.application.add_handler(CommandHandler("check", self.check_command))
        self.application.add_handler(CommandHandler("help", self.help_command))
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /start command"""
        welcome_message = """
🎵 **YUURI Asia Tour 2025 Singapore Bot**

Welcome! I monitor the YUURI concert website for Singapore updates.

**Commands:**
/subscribe - Get notified of Singapore concert updates
/unsubscribe - Stop notifications
/status - Check current Singapore concert info
/check - Force check for updates now
/help - Show this help message

🇸🇬 I focus specifically on Singapore concert details and will notify you when:
- Ticket prices are announced
- Venue details are updated  
- Show times are confirmed
- Booking opens
- Any other significant changes

Use /subscribe to start receiving notifications!
        """
        await update.message.reply_text(welcome_message, parse_mode='Markdown')
    
    async def subscribe_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /subscribe command"""
        chat_id = update.effective_chat.id
        self.subscribers.add(chat_id)
        self.save_subscribers()
        
        await update.message.reply_text(
            "✅ **Subscribed!**\n\n"
            "You'll now receive notifications when Singapore concert details are updated.\n\n"
            "🔄 I check for updates every 5-15 minutes\n"
            "🤖 Using AI when needed for better parsing\n\n"
            "Use /unsubscribe to stop notifications anytime.",
            parse_mode='Markdown'
        )
        
        # Start monitoring if not already running
        if not self.monitoring:
            asyncio.create_task(self.start_monitoring())
    
    async def unsubscribe_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /unsubscribe command"""
        chat_id = update.effective_chat.id
        self.subscribers.discard(chat_id)
        self.save_subscribers()
        
        await update.message.reply_text(
            "❌ **Unsubscribed**\n\n"
            "You won't receive Singapore concert notifications anymore.\n\n"
            "Use /subscribe to re-enable notifications anytime.",
            parse_mode='Markdown'
        )
    
    async def status_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /status command"""
        await update.message.reply_text("🔍 Checking current Singapore concert status...")
        
        try:
            current_data = intelligent_fetch_singapore_data(self.openai_client)
            if current_data and 'singapore' in current_data:
                singapore = current_data['singapore']
                method = current_data.get('method', 'unknown')
                
                status_message = f"""
🇸🇬 **YUURI Singapore Concert Status**

📊 **Data Source:** {method.replace('_', ' ').title()}
📍 **Venue:** {singapore.get('venue', 'TBD')}
📅 **Date:** {singapore.get('date', 'TBD')}
⏰ **Time:** {singapore.get('time', 'TBD')}
🎫 **Tickets:** {singapore.get('tickets', 'TBD')}
📋 **Status:** {singapore.get('status', 'Unknown')}

💰 **Ticket Prices:** {"Available" if singapore.get('ticket_prices') else "Not yet announced"}

{"🎉 **COMPLETE DATA:** All details available!" if singapore.get('is_complete') else "⏳ **PENDING:** Details still being finalized"}
                """
                
                await update.message.reply_text(status_message, parse_mode='Markdown')
            else:
                await update.message.reply_text("❌ Could not fetch Singapore concert data")
                
        except Exception as e:
            logger.error(f"Error in status command: {e}")
            await update.message.reply_text("❌ Error checking status. Please try again later.")
    
    async def check_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /check command - force check for updates"""
        await update.message.reply_text("🔄 Forcing update check...")
        
        try:
            current_data = intelligent_fetch_singapore_data(self.openai_client)
            if current_data:
                if has_significant_singapore_changes(self.previous_data, current_data):
                    await update.message.reply_text("🚨 **CHANGES DETECTED!**", parse_mode='Markdown')
                    await self.send_singapore_update(update.effective_chat.id, current_data)
                    
                    # Save the new data
                    with open('singapore_concert_data.json', 'w', encoding='utf-8') as f:
                        json.dump(current_data, f, indent=2, ensure_ascii=False)
                    self.previous_data = current_data
                else:
                    await update.message.reply_text("✅ No changes detected since last check")
            else:
                await update.message.reply_text("❌ Could not fetch data")
                
        except Exception as e:
            logger.error(f"Error in check command: {e}")
            await update.message.reply_text("❌ Error checking for updates")
    
    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /help command"""
        help_message = """
🎵 **YUURI Singapore Concert Bot Help**

**Commands:**
/start - Welcome message and bot info
/subscribe - Get notified of Singapore updates
/unsubscribe - Stop notifications  
/status - Check current Singapore concert info
/check - Force check for updates now
/help - Show this help

**What I Monitor:**
🇸🇬 Singapore concert details only
🎫 Ticket prices and availability
📍 Venue and timing updates
📅 Important announcements

**How It Works:**
• Checks website every 5-15 minutes
• Uses AI when data is unclear
• Only notifies on significant changes
• Focuses specifically on Singapore

**Setup:**
The bot needs OpenAI API key for intelligent parsing.
Set OPENAI_API_KEY environment variable.
        """
        await update.message.reply_text(help_message, parse_mode='Markdown')
    
    async def send_singapore_update(self, chat_id: int, data: Dict[str, Any]):
        """Send Singapore update to a specific chat"""
        if not data or 'singapore' not in data:
            return
        
        singapore = data['singapore']
        method = data.get('method', 'unknown')
        
        update_message = f"""
🚨 **SINGAPORE CONCERT UPDATE!**

📊 **Source:** {method.replace('_', ' ').title()}
📍 **Venue:** {singapore.get('venue', 'TBD')}
📅 **Date:** {singapore.get('date', 'TBD')}
⏰ **Time:** {singapore.get('time', 'TBD')}
🎫 **Tickets:** {singapore.get('tickets', 'TBD')}
📋 **Status:** {singapore.get('status', 'Unknown')}

💰 **Ticket Prices:**
"""
        
        if singapore.get('ticket_prices'):
            for ticket in singapore['ticket_prices']:
                if isinstance(ticket, dict):
                    update_message += f"• {ticket.get('zone', 'Unknown')}: {ticket.get('price', 'TBD')} ({ticket.get('status', 'Unknown')})\n"
        else:
            update_message += "Not yet announced\n"
        
        if singapore.get('is_complete'):
            update_message += "\n🎉 **COMPLETE DATA:** All Singapore details now available!"
        else:
            update_message += "\n⏳ **PENDING:** Some details still being finalized"
        
        try:
            await self.application.bot.send_message(
                chat_id=chat_id, 
                text=update_message, 
                parse_mode='Markdown'
            )
        except Exception as e:
            logger.error(f"Error sending message to {chat_id}: {e}")
    
    async def start_monitoring(self):
        """Start the monitoring loop"""
        if self.monitoring:
            return
        
        self.monitoring = True
        logger.info("Started Singapore concert monitoring")
        
        while self.monitoring and self.subscribers:
            try:
                current_data = intelligent_fetch_singapore_data(self.openai_client)
                
                if current_data and has_significant_singapore_changes(self.previous_data, current_data):
                    logger.info("Singapore changes detected, notifying subscribers")
                    
                    # Notify all subscribers
                    for chat_id in self.subscribers.copy():  # Copy to avoid modification during iteration
                        await self.send_singapore_update(chat_id, current_data)
                    
                    # Save updated data
                    with open('singapore_concert_data.json', 'w', encoding='utf-8') as f:
                        json.dump(current_data, f, indent=2, ensure_ascii=False)
                    
                    self.previous_data = current_data
                
                # Random sleep interval (5-15 minutes)
                sleep_time = random.randint(300, 900)
                logger.info(f"Sleeping for {sleep_time//60} minutes {sleep_time%60} seconds")
                await asyncio.sleep(sleep_time)
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(300)  # Wait 5 minutes on error
        
        self.monitoring = False
        logger.info("Stopped monitoring (no subscribers)")
    
    def run(self):
        """Run the bot"""
        logger.info("Starting YUURI Singapore Telegram Bot")
        self.load_subscribers()
        
        # Start monitoring if there are subscribers
        if self.subscribers:
            asyncio.create_task(self.start_monitoring())
        
        self.application.run_polling()

def main():
    """Main function"""
    bot_token = os.getenv('TELEGRAM_BOT_TOKEN')
    if not bot_token:
        print("❌ Error: TELEGRAM_BOT_TOKEN environment variable not set")
        print("   Get a bot token from @BotFather on Telegram")
        print("   Then set: export TELEGRAM_BOT_TOKEN='your-bot-token'")
        return
    
    bot = YuuriTelegramBot(bot_token)
    bot.run()

if __name__ == "__main__":
    main()
